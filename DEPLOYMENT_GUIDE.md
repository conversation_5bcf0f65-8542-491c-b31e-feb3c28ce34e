# Lessons Scheduler Service - Deployment Guide

This guide provides instructions for running the Lessons Scheduler Service in different environments (dev, qa, prod) both with and without Docker.

## Table of Contents
- [Prerequisites](#prerequisites)
- [Environment Profiles](#environment-profiles)
- [Running Without Docker](#running-without-docker)
- [Running With Docker](#running-with-docker)
- [Database Configuration](#database-configuration)
- [Troubleshooting](#troubleshooting)

## Prerequisites

### General Requirements
- Java 17 or higher
- Maven 3.6+
- Oracle Database access (for qa/prod profiles)

### For Docker Deployment
- Docker Desktop installed and running
- Docker Compose (optional)

## Environment Profiles

The application supports three profiles:

| Profile | Description | Database | Port |
|---------|-------------|----------|------|
| `dev` | Development environment | Oracle (dev instance) | 8080 |
| `qa` | Quality Assurance environment | Oracle (qa instance) | 8080 |
| `prod` | Production environment | Oracle (prod instance) | 8080 |

## Running Without Docker

### 1. Development Environment (dev)

```bash
# Build the application
mvn clean package -DskipTests

# Run with dev profile (default)
java -jar target/scheduler-3.2.1.jar

# Or explicitly specify dev profile
java -jar target/scheduler-3.2.1.jar --spring.profiles.active=dev

# Or using Maven
mvn spring-boot:run
```

**Configuration**: Uses `application-dev.properties` + `application.properties`

### 2. QA Environment (qa)

```bash
# Build the application
mvn clean package -DskipTests

# Run with qa profile
java -jar target/scheduler-3.2.1.jar --spring.profiles.active=qa

# Or using Maven
mvn spring-boot:run -Dspring-boot.run.profiles=qa
```

**Configuration**: Uses `application-qa.properties`

### 3. Production Environment (prod)

```bash
# Build the application
mvn clean package -DskipTests

# Run with prod profile
java -jar target/scheduler-3.2.1.jar --spring.profiles.active=prod

# Or using Maven
mvn spring-boot:run -Dspring-boot.run.profiles=prod
```

**Configuration**: Uses `application-prod.properties`

## Running With Docker

### 1. Build Docker Image

```bash
# Build the Docker image
docker build -t lessons-scheduler:latest .

# Or build with specific tag
docker build -t lessons-scheduler:dev .
docker build -t lessons-scheduler:qa .
docker build -t lessons-scheduler:prod .
```

### 2. Development Environment (dev)

```bash
# Run dev environment
docker run -d \
  --name lessons-scheduler-dev \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=dev \
  -e spring.security.user.name=admin \
  -e spring.security.user.password=secret \
  lessons-scheduler:latest

# Check logs
docker logs lessons-scheduler-dev

# Stop container
docker stop lessons-scheduler-dev
docker rm lessons-scheduler-dev
```

### 3. QA Environment (qa)

```bash
# Run qa environment
docker run -d \
  --name lessons-scheduler-qa \
  -p 8081:8080 \
  -e SPRING_PROFILES_ACTIVE=qa \
  -e spring.security.user.name=admin \
  -e spring.security.user.password=secret \
  lessons-scheduler:latest

# Check logs
docker logs lessons-scheduler-qa

# Stop container
docker stop lessons-scheduler-qa
docker rm lessons-scheduler-qa
```

### 4. Production Environment (prod)

```bash
# Run production environment
docker run -d \
  --name lessons-scheduler-prod \
  -p 8082:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e spring.security.user.name=admin \
  -e spring.security.user.password=secret \
  lessons-scheduler:latest

# Check logs
docker logs lessons-scheduler-prod

# Stop container
docker stop lessons-scheduler-prod
docker rm lessons-scheduler-prod
```

## Database Configuration

### Current Database Settings

| Environment | Database Type | Host | Port | Database |
|-------------|---------------|------|------|----------|
| dev | Oracle | ora-rac-112-lq-scan.domestic.guitarcenter.com | 1532 | gcstudioqa |
| qa | Oracle | ora-rac-112-lq-scan.domestic.guitarcenter.com | 1532 | gcstudioqa |
| prod | Oracle | ora-rac-112-lq-scan.domestic.guitarcenter.com | 1532 | gcstudioqa |

### Override Database Configuration

You can override database settings using environment variables:

```bash
# For Docker
docker run -d \
  --name lessons-scheduler-custom \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=dev \
  -e jdbc.schedulerDB.url=*********************************************** \
  -e jdbc.schedulerDB.username=your-username \
  -e jdbc.schedulerDB.password=your-password \
  lessons-scheduler:latest

# For local run
java -jar target/scheduler-3.2.1.jar \
  --spring.profiles.active=dev \
  --jdbc.schedulerDB.url=*********************************************** \
  --jdbc.schedulerDB.username=your-username \
  --jdbc.schedulerDB.password=your-password
```

## Application URLs

Once started, the application will be available at:

- **Local/Dev**: http://localhost:8080/lessons-api/v1
- **QA (Docker)**: http://localhost:8081/lessons-api/v1
- **Prod (Docker)**: http://localhost:8082/lessons-api/v1

### Health Check Endpoints

- Health: `/lessons-api/v1/actuator/health` (if actuator is enabled)
- Info: `/lessons-api/v1/actuator/info` (if actuator is enabled)

## Docker Compose (Optional)

Create a `docker-compose.yml` file for easier management:

```yaml
version: '3.8'
services:
  lessons-dev:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - spring.security.user.name=admin
      - spring.security.user.password=secret
    container_name: lessons-scheduler-dev

  lessons-qa:
    build: .
    ports:
      - "8081:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=qa
      - spring.security.user.name=admin
      - spring.security.user.password=secret
    container_name: lessons-scheduler-qa

  lessons-prod:
    build: .
    ports:
      - "8082:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - spring.security.user.name=admin
      - spring.security.user.password=secret
    container_name: lessons-scheduler-prod
```

Run with Docker Compose:
```bash
# Start all environments
docker-compose up -d

# Start specific environment
docker-compose up -d lessons-dev

# Stop all
docker-compose down

# View logs
docker-compose logs lessons-dev
```

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Check what's using the port
   lsof -i :8080
   
   # Kill the process or use different port
   docker run -p 8090:8080 ...
   ```

2. **Database Connection Issues**
   ```bash
   # Check database connectivity
   telnet ora-rac-112-lq-scan.domestic.guitarcenter.com 1532
   
   # Verify credentials in application properties
   ```

3. **Profile Not Loading**
   ```bash
   # Check logs for active profile
   docker logs lessons-scheduler-dev | grep "profile is active"
   ```

4. **Memory Issues**
   ```bash
   # Increase memory for Docker
   docker run -m 2g ...
   
   # Or for local Java
   java -Xmx2g -jar target/scheduler-3.2.1.jar
   ```

### Useful Commands

```bash
# View running containers
docker ps

# View all containers
docker ps -a

# View container logs
docker logs <container-name>

# Execute command in running container
docker exec -it <container-name> /bin/bash

# Remove all stopped containers
docker container prune

# Remove unused images
docker image prune
```

## Security Notes

- Default credentials are set to `admin/secret` for development
- Change credentials for production deployments
- Ensure Oracle database credentials are properly secured
- Consider using Docker secrets or environment files for sensitive data

## Performance Tuning

### JVM Options
```bash
# For production
java -Xms1g -Xmx2g -XX:+UseG1GC -jar target/scheduler-3.2.1.jar

# For Docker
docker run -e JAVA_OPTS="-Xms1g -Xmx2g -XX:+UseG1GC" ...
```

### Database Connection Pool
Adjust connection pool settings in application properties:
```properties
# Example for high-load environments
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
```
